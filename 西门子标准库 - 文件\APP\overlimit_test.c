/* 
 * 超限数据存储测试文件
 * 用于验证超限数据存储功能
 */

#include "sampling_app.h"
#include "rtc_app.h"
#include "config_app.h"

/**
 * @brief 测试超限数据存储功能
 * 模拟12条超限数据，验证文件分割功能
 */
void test_overlimit_storage(void)
{
    // 初始化存储系统
    sampling_storage_init();
    
    // 模拟配置参数
    extern config_params_t config_params;
    config_params.limit = 10.0f; // 设置限制值为10V
    
    // 模拟时间设置
    extern rtc_time_t current_time;
    current_time.year = 25;   // 2025年
    current_time.month = 1;   // 1月
    current_time.day = 1;     // 1日
    current_time.hour = 0;    // 0时
    current_time.minute = 30; // 30分
    current_time.second = 10; // 10秒
    
    my_printf(DEBUG_USART, "=== 开始测试超限数据存储 ===\r\n");
    
    // 模拟12条超限数据（会创建2个文件）
    float test_voltages[] = {
        15.5f, 12.3f, 18.7f, 11.2f, 20.1f,  // 第1个文件：5条数据
        13.8f, 16.4f, 14.9f, 17.2f, 19.6f,  // 第1个文件：再5条数据（共10条）
        22.3f, 25.1f                        // 第2个文件：2条数据
    };
    
    for(int i = 0; i < 12; i++) {
        // 更新时间（每次增加1秒）
        current_time.second += 1;
        if(current_time.second >= 60) {
            current_time.second = 0;
            current_time.minute += 1;
        }
        
        // 获取时间字符串
        char time_str[32];
        rtc_get_time_string(time_str, sizeof(time_str));
        
        // 保存超限数据
        my_printf(DEBUG_USART, "保存第%d条超限数据: %s %.0fV limit %.0fV\r\n", 
                 i+1, time_str, test_voltages[i], config_params.limit);
        
        sampling_save_overlimit_data(time_str, test_voltages[i], config_params.limit);
        
        // 显示当前文件状态
        my_printf(DEBUG_USART, "当前文件: %s, 记录数: %d\r\n", 
                 file_storage.current_overlimit_filename, 
                 file_storage.overlimit_record_count);
        
        // 延时100ms
        delay_ms(100);
    }
    
    my_printf(DEBUG_USART, "=== 超限数据存储测试完成 ===\r\n");
    my_printf(DEBUG_USART, "应该生成2个文件：\r\n");
    my_printf(DEBUG_USART, "1. 第一个文件包含10条记录\r\n");
    my_printf(DEBUG_USART, "2. 第二个文件包含2条记录\r\n");
    my_printf(DEBUG_USART, "文件名格式: overLimit20250101003010.txt\r\n");
    my_printf(DEBUG_USART, "存储格式: 2025-01-01 00:30:10 30V limit 10V\r\n");
}

/**
 * @brief 验证文件内容格式
 * 读取并显示overLimit文件夹中的文件内容
 */
void verify_overlimit_files(void)
{
    DIR dir;
    FILINFO fno;
    FIL file;
    FRESULT result;
    char buffer[256];
    UINT bytes_read;
    
    my_printf(DEBUG_USART, "=== 验证overLimit文件内容 ===\r\n");
    
    // 打开overLimit文件夹
    result = f_opendir(&dir, OVERLIMIT_FOLDER_PATH);
    if(result != FR_OK) {
        my_printf(DEBUG_USART, "无法打开overLimit文件夹: %d\r\n", result);
        return;
    }
    
    // 遍历文件夹中的文件
    while(1) {
        result = f_readdir(&dir, &fno);
        if(result != FR_OK || fno.fname[0] == 0) break; // 读取完毕或出错
        
        if(!(fno.fattrib & AM_DIR)) { // 如果是文件
            char full_path[128];
            snprintf(full_path, sizeof(full_path), "%s/%s", OVERLIMIT_FOLDER_PATH, fno.fname);
            
            my_printf(DEBUG_USART, "\n--- 文件: %s ---\r\n", fno.fname);
            
            // 打开文件
            result = f_open(&file, full_path, FA_READ);
            if(result == FR_OK) {
                // 读取并显示文件内容
                while(f_gets(buffer, sizeof(buffer), &file)) {
                    my_printf(DEBUG_USART, "%s", buffer);
                }
                f_close(&file);
            } else {
                my_printf(DEBUG_USART, "无法打开文件: %d\r\n", result);
            }
        }
    }
    
    f_closedir(&dir);
    my_printf(DEBUG_USART, "=== 文件内容验证完成 ===\r\n");
}
