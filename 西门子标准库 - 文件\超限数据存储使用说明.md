# 超限数据存储功能使用说明

## 功能概述

超限数据存储功能会自动将超过设定阈值的采样数据存储到TF卡的`overLimit`文件夹中，实现实时超限数据记录和管理。

## 存储规格

### 文件存储规则
- **每个文件存储条数**: 10条数据
- **文件分割**: 超过10条后自动新建文件
- **文件命名**: `overLimit{datetime}.txt`
- **datetime格式**: 连续14个数字（YYYYMMDDHHmmss）

### 数据格式
- **存储格式**: `YYYY-MM-DD HH:MM:SS XXV limit YYV`
- **示例**: `2025-01-01 00:30:10 30V limit 10V`

## 使用步骤

### 1. 系统初始化
```
====system init====
Device_ID:2025-CIMC-xxxxxxxx
```

### 2. 设置系统时间
```
> RTC Config
Input Datetime
> 2025-01-01 00:30:10
RTC Config success
Time:2025-01-01 00:30:10
```

### 3. 配置超限阈值
```
> limit
Limit = 100.00
Input value(0~200)
> 10
limit modified success
Limit = 10.00
```

### 4. 开始采样
```
> start
Period Sampling
sample cycle :5s
2025-01-01 00:30:10 ch0 = 8.50V
2025-01-01 00:30:15 ch0 = 12.30V, OverLimit(10.00)!
2025-01-01 00:30:20 ch0 = 15.60V, OverLimit(10.00)!
```

### 5. 查看存储结果
超限数据会自动存储到 `0:/overLimit/overLimit20250101003010.txt` 文件中：
```
2025-01-01 00:30:15 12V limit 10V
2025-01-01 00:30:20 16V limit 10V
```

## 测试功能

### 测试超限存储
在您的代码中添加测试函数调用：
```c
// 在main函数中添加
#include "overlimit_test.h"

// 测试超限数据存储
test_overlimit_storage();

// 验证文件内容
verify_overlimit_files();
```

### 预期测试结果
- 生成2个overLimit文件
- 第一个文件包含10条记录
- 第二个文件包含2条记录
- 文件名格式正确
- 数据格式符合要求

## LED指示说明

- **LED1**: 采样状态指示（1秒周期闪烁）
- **LED2**: 超限状态指示
  - 超限时：点亮
  - 正常时：熄灭

## 注意事项

1. **TF卡要求**
   - 确保TF卡已正确插入
   - 格式化为FAT32格式
   - 有足够的存储空间

2. **时间设置**
   - 文件名依赖系统时间
   - 请确保RTC时间设置正确

3. **参数配置**
   - limit参数决定超限阈值
   - ratio参数影响电压显示值

4. **文件管理**
   - 系统自动创建overLimit文件夹
   - 每个文件最多10条记录
   - 超过后自动创建新文件

## 故障排除

### 文件未生成
- 检查TF卡是否正确插入
- 运行`test`命令检查TF卡状态
- 确认系统时间设置正确

### 数据格式错误
- 检查RTC时间设置
- 确认limit参数配置正确
- 验证电压计算公式

### 文件写入失败
- 检查TF卡写保护状态
- 确认TF卡剩余空间
- 查看串口调试信息

## 技术参数

- **文件系统**: FAT32
- **存储路径**: `0:/overLimit/`
- **文件编码**: ASCII
- **时间精度**: 秒级
- **电压精度**: 整数显示（如30V、10V）
- **最大文件数**: 无限制（受TF卡容量限制）
